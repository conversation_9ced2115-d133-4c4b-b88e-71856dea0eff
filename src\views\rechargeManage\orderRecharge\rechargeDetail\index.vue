<template>
  <div>
    <!-- 列表:  -->
    <BasicTable v-bind="stateTable" :summaryData="summaryData" @register="registerTable">
    <template #tableTitle>
      <a-button v-if="showAppleModal" type="primary" class="mr-2" @click="handleAppleOrderQuery">
        苹果订单查询
      </a-button>
    </template>
      <template #PAY_RESULT_NAME="{ record }">
        <div :class="record.PAY_RESULT > 0 ? 'green' : 'red'">{{ record.PAY_RESULT_NAME }}</div>
      </template>
      <template #GAME_RESULT_NAME="{ record }">
        <div :class="record.GAME_RESULT > 0 ? 'green' : 'red'">{{ record.GAME_RESULT_NAME }}</div>
      </template>
      <template #ORDER_ID="{ text, record }">
        <Tooltip placement="topLeft" trigger="hover" :destroyTooltipOnHide="true" :title="text">
          <div
            @click="handlerCreate(record)"
            :class="record.PAY_RESULT > 0 && record.GAME_RESULT == 0 ? 'red-back' : ''"
            >{{ record.ORDER_ID }}</div
          >
        </Tooltip>
      </template>
    </BasicTable>

    <EditDrawer @register="drawerEdit.register" title="客服补单" @Update="drawerEdit.closeDrawer" />

    <!-- 苹果订单查询弹窗 -->
    <AppleOrderQueryModal v-if="showAppleModal" @register="appleOrderModal.register" />

  </div>
</template>
<script lang="ts">
  import { defineComponent, reactive, toRaw, ref, nextTick, unref, computed } from 'vue';
  import { getTableSetting, searchFormConfig } from './config';
  import { BasicTable, TableAction, useTable } from '/@/components/Table';
  import EditDrawer from './editDrawer/index.vue';
  import AppleOrderQueryModal from './AppleModal.vue';
  // import { useDrawer } from '/@/components/Drawer';
  import { getCommonOptions } from '/@/api/sys/common';
  import { usePermission } from '/@/hooks/web/usePermission';
  // import { useMessage } from '/@/hooks/web/useMessage';
  import { copyText } from '/@/utils/domUtils';
  import { Tooltip } from 'ant-design-vue';
  import { useDrawerModel } from '/@/utils/components/useDrawerModel';
  import type { drawerModuleRetures } from '/@/utils/components/useDrawerModel';
  import { useModal } from '/@/components/Modal';
  import { userStore } from '/@/store/modules/user';
  import {
    getSummary,
    getList,
    exportListQueue,
  } from '/@/api/rechargeManage/orderRecharge/rechargeDetail';
  export default defineComponent({
    name: 'RechargeDetail',
    components: { BasicTable, TableAction, EditDrawer, Tooltip, AppleOrderQueryModal },
    setup() {
      const { getAuth } = usePermission();
      const AUTH = getAuth();
      // const { createMessage } = useMessage();
      // const drawerTitle = ref('新增');

      // 判断是否显示苹果订单查询功能
      const showAppleModal = computed(() => {
        const userInfo = userStore.getUserInfoState;
        return userInfo?.user_name === 'admin';
      });

      // 注册苹果订单查询弹窗
      const [registerAppleOrderModal, { openModal: openAppleOrderModal }] = useModal();

      //表格异步数据配置
      const stateTable = reactive({
        columns: [],
        formConfig: {},
      });

      const summaryData = ref([]);
      const getSummaryData = async (params, redoHeight) => {
        const data = await getSummary(params);
        summaryData.value = [
          {
            ID: '合计',
            AMOUNT: data.amount_total,
            REAL_AMOUNT: data.amount_real,
          },
        ];
        nextTick(() => {
          redoHeight();
        });
      };

      // 注册表格
      const [registerTable, { redoHeight, reload, getForm }] = useTable({
        api: getList,
        downloadApi: exportListQueue,
        immediate: false,
        // 显示汇总数据
        showSummary: true,
        afterFetch: (data) => {
          getSummaryData(getForm().getFieldsValue(), redoHeight);
          return data;
        },
        summaryFunc: () => {
          return unref(summaryData);
        },
        ...getTableSetting(AUTH),
      });

      // 抽屉注册
      let drawerInstance = {
        drawerEdit: useDrawerModel({ reload }) as drawerModuleRetures, // 编辑
      };

      // TODO 根据需求修改 表头和选项数据
      getCommonOptions({
        schema: 'payment',
        options: 'cp_games|payways|games|spread_channels|order_recharge_status|spread_main_channel',
      }).then((data) => {
        stateTable.columns = data.columns;
        stateTable.formConfig = searchFormConfig(data.options);
        nextTick(() => {
          reload();
        });
      });

      // 新增
      const handlerCreate = (record) => {
        // && AUTH.create
        if (record.PAY_RESULT > 0 && record.GAME_RESULT == 0) {
          drawerInstance.drawerEdit.sendData({ columns: toRaw(record) });
        } else {
          copyText(record.ORDER_ID);
        }
      };

      const handleAppleOrderQuery = () => {
        openAppleOrderModal(true, {});
      };

      return {
        stateTable,
        registerTable,
        handlerCreate,
        ...drawerInstance,
        AUTH,
        summaryData,
        // 苹果订单查询相关
        showAppleModal,
        handleAppleOrderQuery,
        appleOrderModal: {
          register: registerAppleOrderModal,
        },
      };
    },
  });
</script>
<style scoped>
  .green {
    color: green;
  }

  .red {
    color: red;
  }

  .red-back {
    cursor: pointer;
    background-color: red;
  }
</style>
