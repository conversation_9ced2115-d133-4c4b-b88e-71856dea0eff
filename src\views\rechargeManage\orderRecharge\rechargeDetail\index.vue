<template>
  <div>
    <!-- 列表:  -->
    <BasicTable v-bind="stateTable" :summaryData="summaryData" @register="registerTable">
    <template #tableTitle>
      <a-button type="primary" class="mr-2" @click="handleAppleOrderQuery">
        苹果订单查询
      </a-button>
    </template>
      <template #PAY_RESULT_NAME="{ record }">
        <div :class="record.PAY_RESULT > 0 ? 'green' : 'red'">{{ record.PAY_RESULT_NAME }}</div>
      </template>
      <template #GAME_RESULT_NAME="{ record }">
        <div :class="record.GAME_RESULT > 0 ? 'green' : 'red'">{{ record.GAME_RESULT_NAME }}</div>
      </template>
      <template #ORDER_ID="{ text, record }">
        <Tooltip placement="topLeft" trigger="hover" :destroyTooltipOnHide="true" :title="text">
          <div
            @click="handlerCreate(record)"
            :class="record.PAY_RESULT > 0 && record.GAME_RESULT == 0 ? 'red-back' : ''"
            >{{ record.ORDER_ID }}</div
          >
        </Tooltip>
      </template>
    </BasicTable>

    <EditDrawer @register="drawerEdit.register" title="客服补单" @Update="drawerEdit.closeDrawer" />

    <!-- 苹果订单查询弹窗 -->
    <BasicModal
      @register="appleOrderModal.register"
      title="苹果订单查询"
      width="600px"
      @ok="handleAppleOrderSubmit"
      @cancel="handleAppleOrderCancel"
    >
      <BasicForm @register="registerAppleOrderForm" />

      <!-- 查询结果显示区域 -->
      <div v-if="appleOrderResult" class="mt-4">
        <a-divider>查询结果</a-divider>
        <pre class="result-display">{{ JSON.stringify(appleOrderResult, null, 2) }}</pre>
      </div>
    </BasicModal>

  </div>
</template>
<script lang="ts">
  import { defineComponent, reactive, toRaw, ref, nextTick, unref } from 'vue';
  import { getTableSetting, searchFormConfig } from './config';
  import { BasicTable, TableAction, useTable } from '/@/components/Table';
  import EditDrawer from './editDrawer/index.vue';
  // import { useDrawer } from '/@/components/Drawer';
  import { getCommonOptions } from '/@/api/sys/common';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { copyText } from '/@/utils/domUtils';
  import { Tooltip, Divider } from 'ant-design-vue';
  import { useDrawerModel } from '/@/utils/components/useDrawerModel';
  import type { drawerModuleRetures } from '/@/utils/components/useDrawerModel';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { defHttp } from '/@/utils/http/axios';
  import {
    getSummary,
    getList,
    exportListQueue,
  } from '/@/api/rechargeManage/orderRecharge/rechargeDetail';
  export default defineComponent({
    name: 'RechargeDetail',
    components: { BasicTable, TableAction, EditDrawer, Tooltip, BasicModal, BasicForm },
    setup() {
      const { getAuth } = usePermission();
      const AUTH = getAuth();
      const { createMessage } = useMessage();
      // const drawerTitle = ref('新增');

      // 苹果订单查询相关
      const appleOrderResult = ref(null);

      // 苹果订单查询表单配置
      const appleOrderFormSchemas: FormSchema[] = [
        {
          field: 'orderId',
          label: '订单号',
          component: 'Input',
          required: true,
          colProps: { span: 24 },
          componentProps: {
            placeholder: '请输入订单号',
          },
        },
        {
          field: 'appleOrderId',
          label: '苹果订单',
          component: 'Input',
          required: true,
          colProps: { span: 24 },
          componentProps: {
            placeholder: '请输入苹果订单号',
          },
        },
        {
          field: 'queryType',
          label: '查询类型',
          component: 'RadioButtonGroup',
          required: true,
          colProps: { span: 24 },
          defaultValue: 'apple_order',
          componentProps: {
            options: [
              { label: '苹果订单', value: 'apple_order' },
              { label: '支付凭证', value: 'payment_receipt' },
            ],
          },
        },
      ];

      // 注册苹果订单查询表单
      const [registerAppleOrderForm, { validateFields: validateAppleOrderFields, resetFields: resetAppleOrderFields }] = useForm({
        labelWidth: 100,
        schemas: appleOrderFormSchemas,
        showActionButtonGroup: false,
        actionColOptions: {
          span: 24,
        },
      });

      // 注册苹果订单查询弹窗
      const [registerAppleOrderModal, { openModal: openAppleOrderModal, setModalProps: setAppleOrderModalProps }] = useModal();

      //表格异步数据配置
      const stateTable = reactive({
        columns: [],
        formConfig: {},
      });

      const summaryData = ref([]);
      const getSummaryData = async (params, redoHeight) => {
        const data = await getSummary(params);
        summaryData.value = [
          {
            ID: '合计',
            AMOUNT: data.amount_total,
            REAL_AMOUNT: data.amount_real,
          },
        ];
        nextTick(() => {
          redoHeight();
        });
      };

      // 注册表格
      const [registerTable, { redoHeight, reload, getForm }] = useTable({
        api: getList,
        downloadApi: exportListQueue,
        immediate: false,
        // 显示汇总数据
        showSummary: true,
        afterFetch: (data) => {
          getSummaryData(getForm().getFieldsValue(), redoHeight);
          return data;
        },
        summaryFunc: () => {
          return unref(summaryData);
        },
        ...getTableSetting(AUTH),
      });

      // 抽屉注册
      let drawerInstance = {
        drawerEdit: useDrawerModel({ reload }) as drawerModuleRetures, // 编辑
      };

      // TODO 根据需求修改 表头和选项数据
      getCommonOptions({
        schema: 'payment',
        options: 'cp_games|payways|games|spread_channels|order_recharge_status|spread_main_channel',
      }).then((data) => {
        stateTable.columns = data.columns;
        stateTable.formConfig = searchFormConfig(data.options);
        nextTick(() => {
          reload();
        });
      });

      // 新增
      const handlerCreate = (record) => {
        // && AUTH.create
        if (record.PAY_RESULT > 0 && record.GAME_RESULT == 0) {
          drawerInstance.drawerEdit.sendData({ columns: toRaw(record) });
        } else {
          copyText(record.ORDER_ID);
        }
      };

      // 苹果订单查询相关函数
      const handleAppleOrderQuery = () => {
        appleOrderResult.value = null;
        resetAppleOrderFields();
        openAppleOrderModal(true);
      };

      const handleAppleOrderSubmit = async () => {
        try {
          const values = await validateAppleOrderFields();
          setAppleOrderModalProps({ confirmLoading: true });

          // 调用苹果订单查询API
          const result = await defHttp.post({
            url: '/pay/payment/ios',
            data: {
              order_id: values.orderId,
              apple_order_id: values.appleOrderId,
              query_type: values.queryType,
            },
          });

          appleOrderResult.value = result;
          createMessage.success('查询成功');
        } catch (error) {
          console.error('苹果订单查询失败:', error);
          createMessage.error('查询失败，请重试');
        } finally {
          setAppleOrderModalProps({ confirmLoading: false });
        }
      };

      const handleAppleOrderCancel = () => {
        appleOrderResult.value = null;
        setAppleOrderModalProps({ visible: false });
      };

      return {
        stateTable,
        registerTable,
        handlerCreate,
        ...drawerInstance,
        AUTH,
        summaryData,
      };
    },
  });
</script>
<style scoped>
  .green {
    color: green;
  }

  .red {
    color: red;
  }

  .red-back {
    cursor: pointer;
    background-color: red;
  }
</style>
