<template>
  <BasicModal
    @register="register"
    title="苹果订单查询"
    width="600px"
    :height="500"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <BasicForm @register="registerForm" />

    <div v-if="queryResult" class="mt-4">
      <a-divider>查询结果</a-divider>
      <pre class="result-display">{{ JSON.stringify(queryResult, null, 2) }}</pre>
    </div>
  </BasicModal>
</template>

<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { defHttp } from '/@/utils/http/axios';
  import { Divider } from 'ant-design-vue';

  export default defineComponent({
    name: 'AppleOrderQueryModal',
    components: { BasicModal, BasicForm },
    emits: ['register'],
    setup(props, { emit }) {
      const { createMessage } = useMessage();
      const queryResult = ref(null);

      // 苹果订单查询表单配置
      const formSchemas: FormSchema[] = [
        {
          field: 'orderId',
          label: '订单号',
          component: 'Input',
          required: true,
          colProps: { span: 24 },
          componentProps: {
            placeholder: '请输入订单号',
          },
        },
        {
          field: 'appleOrderId',
          label: '苹果订单',
          component: 'Input',
          required: true,
          colProps: { span: 24 },
          componentProps: {
            placeholder: '请输入苹果订单号',
          },
        },
        {
          field: 'queryType',
          label: '查询类型',
          component: 'RadioButtonGroup',
          required: true,
          colProps: { span: 24 },
          defaultValue: 'apple_order',
          componentProps: {
            options: [
              { label: '苹果订单', value: 'apple_order' },
              { label: '支付凭证', value: 'payment_receipt' },
            ],
          },
        },
      ];

      // 注册表单
      const [registerForm, { validateFields, resetFields }] = useForm({
        labelWidth: 100,
        schemas: formSchemas,
        showActionButtonGroup: false,
        actionColOptions: {
          span: 24,
        },
      });

      // 注册弹窗
      const [register, { closeModal, setModalProps }] = useModalInner(() => {
        // 每次打开弹窗时重置表单和结果
        queryResult.value = null;
        resetFields();
        // 可以在这里动态设置Modal属性
        // setModalProps({ height: 600 });
      });

      // 提交查询
      const handleSubmit = async () => {
        try {
          const values = await validateFields();
          setModalProps({ confirmLoading: true });
          const result = await defHttp.post({
            url: '/pay/payment/ios',
            params: {
              order_id: values.orderId,
              apple_order_id: values.appleOrderId,
              query_type: values.queryType,
            },
          });

          queryResult.value = result;
          createMessage.success('查询成功');
        } catch (error) {
          console.error('苹果订单查询失败:', error);
        } finally {
          setModalProps({ confirmLoading: false });
        }
      };

      // 取消操作
      const handleCancel = () => {
        queryResult.value = null;
        closeModal();
      };

      return {
        register,
        registerForm,
        queryResult,
        handleSubmit,
        handleCancel,
      };
    },
  });
</script>

<style scoped>
  .result-display {
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-all;
  }
</style>
